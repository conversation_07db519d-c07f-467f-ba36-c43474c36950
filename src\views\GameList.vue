<template>
  <div class="game-list-container">
    <!-- 页面头部 -->
    <n-page-header title="游戏库" subtitle="管理和浏览您的游戏收藏">
      <template #extra>
        <n-space>
          <n-button type="primary" @click="showAddGameModal = true">
            ➕ 添加游戏
          </n-button>
          <n-button @click="refreshGameList">
            🔄 刷新
          </n-button>
        </n-space>
      </template>
    </n-page-header>

    <!-- 搜索和筛选区域 -->
    <n-card class="search-card" :bordered="false">
      <n-space vertical>
        <n-space>
          <n-input
            v-model:value="searchQuery"
            placeholder="🔍 搜索游戏名称..."
            clearable
            style="width: 300px"
          />
          
          <n-select
            v-model:value="selectedGenre"
            placeholder="选择类型"
            clearable
            style="width: 150px"
            :options="genreOptions"
          />
          
          <n-select
            v-model:value="selectedStatus"
            placeholder="游戏状态"
            clearable
            style="width: 150px"
            :options="statusOptions"
          />
        </n-space>
        
        <n-space>
          <n-text depth="3">显示方式：</n-text>
          <n-radio-group v-model:value="viewMode">
            <n-radio-button value="grid">网格</n-radio-button>
            <n-radio-button value="list">列表</n-radio-button>
          </n-radio-group>
        </n-space>
      </n-space>
    </n-card>

    <!-- 游戏列表 -->
    <n-card :bordered="false">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="game-grid">
        <n-card
          v-for="game in filteredGames"
          :key="game.id"
          class="game-card"
          hoverable
          @click="selectGame(game)"
        >
          <div class="game-cover">
            <n-image
              :src="game.cover"
              :alt="game.name"
              fallback-src="/api/placeholder/200/280"
              object-fit="cover"
            />
            <div class="game-overlay">
              <n-space>
                <n-button circle type="primary" @click.stop="playGame(game)">
                  ▶️
                </n-button>
                <n-button circle @click.stop="editGame(game)">
                  ✏️
                </n-button>
              </n-space>
            </div>
          </div>
          <div class="game-info">
            <n-text strong>{{ game.name }}</n-text>
            <n-text depth="3" class="game-genre">{{ game.genre }}</n-text>
            <div class="game-meta">
              <n-tag :type="getStatusType(game.status)" size="small">
                {{ game.status }}
              </n-tag>
              <n-text depth="3" class="play-time">
                {{ formatPlayTime(game.playTime) }}
              </n-text>
            </div>
          </div>
        </n-card>
      </div>

      <!-- 列表视图 -->
      <n-data-table
        v-else
        :columns="tableColumns"
        :data="filteredGames"
        :pagination="pagination"
        :row-key="(row) => row.id"
        @update:checked-row-keys="handleCheck"
      />
    </n-card>

    <!-- 添加游戏模态框 -->
    <n-modal v-model:show="showAddGameModal" preset="dialog" title="添加游戏">
      <n-form ref="formRef" :model="newGame" :rules="formRules">
        <n-form-item label="游戏名称" path="name">
          <n-input v-model:value="newGame.name" placeholder="请输入游戏名称" />
        </n-form-item>
        <n-form-item label="游戏类型" path="genre">
          <n-select
            v-model:value="newGame.genre"
            placeholder="选择游戏类型"
            :options="genreOptions"
          />
        </n-form-item>
        <n-form-item label="游戏状态" path="status">
          <n-select
            v-model:value="newGame.status"
            placeholder="选择游戏状态"
            :options="statusOptions"
          />
        </n-form-item>
        <n-form-item label="游戏路径" path="path">
          <n-input v-model:value="newGame.path" placeholder="游戏可执行文件路径" />
        </n-form-item>
        <n-form-item label="封面图片" path="cover">
          <n-input v-model:value="newGame.cover" placeholder="封面图片URL（可选）" />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showAddGameModal = false">取消</n-button>
          <n-button type="primary" @click="addGame">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import {
  NPageHeader,
  NCard,
  NSpace,
  NButton,
  NIcon,
  NInput,
  NSelect,
  NRadioGroup,
  NRadioButton,
  NText,
  NImage,
  NTag,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  useMessage,
  type DataTableColumns
} from 'naive-ui'


// 消息提示
const message = useMessage()

// 响应式数据
const searchQuery = ref('')
const selectedGenre = ref(null)
const selectedStatus = ref(null)
const viewMode = ref('grid')
const showAddGameModal = ref(false)

// 游戏数据
interface Game {
  id: number
  name: string
  genre: string
  status: string
  playTime: number
  cover: string
  path: string
  lastPlayed?: Date
}

const games = ref<Game[]>([
  {
    id: 1,
    name: '赛博朋克 2077',
    genre: 'RPG',
    status: '已完成',
    playTime: 120,
    cover: 'https://via.placeholder.com/200x280/4A90E2/FFFFFF?text=Cyberpunk',
    path: 'C:/Games/Cyberpunk2077/bin/x64/Cyberpunk2077.exe'
  },
  {
    id: 2,
    name: '巫师3：狂猎',
    genre: 'RPG',
    status: '进行中',
    playTime: 85,
    cover: 'https://via.placeholder.com/200x280/50C878/FFFFFF?text=Witcher3',
    path: 'C:/Games/TheWitcher3/bin/x64/witcher3.exe'
  },
  {
    id: 3,
    name: 'CS2',
    genre: 'FPS',
    status: '经常玩',
    playTime: 300,
    cover: 'https://via.placeholder.com/200x280/FF6B6B/FFFFFF?text=CS2',
    path: 'steam://rungameid/730'
  }
])

// 选项数据
const genreOptions = [
  { label: 'RPG', value: 'RPG' },
  { label: 'FPS', value: 'FPS' },
  { label: '动作', value: '动作' },
  { label: '策略', value: '策略' },
  { label: '模拟', value: '模拟' },
  { label: '竞速', value: '竞速' }
]

const statusOptions = [
  { label: '未开始', value: '未开始' },
  { label: '进行中', value: '进行中' },
  { label: '已完成', value: '已完成' },
  { label: '经常玩', value: '经常玩' },
  { label: '已放弃', value: '已放弃' }
]

// 新游戏表单
const newGame = ref({
  name: '',
  genre: '',
  status: '',
  path: '',
  cover: ''
})

const formRules = {
  name: { required: true, message: '请输入游戏名称', trigger: 'blur' },
  genre: { required: true, message: '请选择游戏类型', trigger: 'change' },
  status: { required: true, message: '请选择游戏状态', trigger: 'change' },
  path: { required: true, message: '请输入游戏路径', trigger: 'blur' }
}

// 计算属性
const filteredGames = computed(() => {
  return games.value.filter(game => {
    const matchesSearch = !searchQuery.value || 
      game.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    const matchesGenre = !selectedGenre.value || game.genre === selectedGenre.value
    const matchesStatus = !selectedStatus.value || game.status === selectedStatus.value
    
    return matchesSearch && matchesGenre && matchesStatus
  })
})

// 表格配置
const pagination = { pageSize: 10 }
const tableColumns: DataTableColumns<Game> = [
  { title: '游戏名称', key: 'name' },
  { title: '类型', key: 'genre' },
  { title: '状态', key: 'status' },
  { 
    title: '游戏时长', 
    key: 'playTime',
    render: (row) => formatPlayTime(row.playTime)
  },
  {
    title: '操作',
    key: 'actions',
    render: (row) => h(NSpace, null, {
      default: () => [
        h(NButton, { size: 'small', onClick: () => playGame(row) }, { default: () => '启动' }),
        h(NButton, { size: 'small', onClick: () => editGame(row) }, { default: () => '编辑' })
      ]
    })
  }
]

// 方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, any> = {
    '未开始': 'default',
    '进行中': 'info',
    '已完成': 'success',
    '经常玩': 'warning',
    '已放弃': 'error'
  }
  return typeMap[status] || 'default'
}

const formatPlayTime = (minutes: number) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours}h ${mins}m`
}

const selectGame = (game: Game) => {
  message.info(`选择了游戏: ${game.name}`)
}

const playGame = (game: Game) => {
  message.success(`启动游戏: ${game.name}`)
  // 这里可以添加启动游戏的逻辑
}

const editGame = (game: Game) => {
  message.info(`编辑游戏: ${game.name}`)
  // 这里可以添加编辑游戏的逻辑
}

const addGame = () => {
  const newId = Math.max(...games.value.map(g => g.id)) + 1
  games.value.push({
    id: newId,
    ...newGame.value,
    playTime: 0,
    cover: newGame.value.cover || `https://via.placeholder.com/200x280/999999/FFFFFF?text=${encodeURIComponent(newGame.value.name)}`
  })
  
  message.success('游戏添加成功！')
  showAddGameModal.value = false
  
  // 重置表单
  newGame.value = {
    name: '',
    genre: '',
    status: '',
    path: '',
    cover: ''
  }
}

const refreshGameList = () => {
  message.info('刷新游戏列表')
  // 这里可以添加刷新逻辑
}

const handleCheck = (keys: any[]) => {
  console.log('选中的游戏:', keys)
}
</script>

<style scoped>
.game-list-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.search-card {
  margin: 20px 0;
}

.game-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.game-card {
  position: relative;
  transition: transform 0.2s ease;
}

.game-card:hover {
  transform: translateY(-5px);
}

.game-cover {
  position: relative;
  height: 280px;
  overflow: hidden;
  border-radius: 6px;
}

.game-cover img {
  width: 100%;
  height: 100%;
}

.game-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.game-card:hover .game-overlay {
  opacity: 1;
}

.game-info {
  padding: 12px 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.game-genre {
  font-size: 12px;
}

.game-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.play-time {
  font-size: 12px;
}
</style>
