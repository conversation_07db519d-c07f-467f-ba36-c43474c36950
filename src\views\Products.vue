<template>
  <div class="products">
    <div class="container">
      <h1>产品展示</h1>
      <div class="products-grid">
        <div 
          v-for="product in products" 
          :key="product.id"
          class="product-card"
          @click="selectProduct(product)"
          :class="{ active: selectedProduct?.id === product.id }"
        >
          <div class="product-image">
            {{ product.icon }}
          </div>
          <h3>{{ product.name }}</h3>
          <p class="description">{{ product.description }}</p>
          <div class="price">{{ product.price }}</div>
          <div class="features">
            <span 
              v-for="feature in product.features" 
              :key="feature"
              class="feature-tag"
            >
              {{ feature }}
            </span>
          </div>
        </div>
      </div>

      <div v-if="selectedProduct" class="product-detail">
        <h2>{{ selectedProduct.name }} - 详细信息</h2>
        <div class="detail-content">
          <div class="detail-image">
            {{ selectedProduct.icon }}
          </div>
          <div class="detail-info">
            <p class="detail-description">{{ selectedProduct.detailDescription }}</p>
            <div class="specifications">
              <h4>产品规格：</h4>
              <ul>
                <li v-for="spec in selectedProduct.specifications" :key="spec">
                  {{ spec }}
                </li>
              </ul>
            </div>
            <div class="actions">
              <button class="btn btn-primary">立即购买</button>
              <button class="btn btn-secondary">加入购物车</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

interface Product {
  id: number
  name: string
  description: string
  detailDescription: string
  price: string
  icon: string
  features: string[]
  specifications: string[]
}

const selectedProduct = ref<Product | null>(null)

const products = reactive<Product[]>([
  {
    id: 1,
    name: 'Vue 开发套件',
    description: '完整的 Vue.js 开发解决方案',
    detailDescription: '这是一个完整的 Vue.js 开发套件，包含了现代化的开发工具和最佳实践。适合快速构建高质量的 Web 应用程序。',
    price: '¥299',
    icon: '🚀',
    features: ['Vue 3', 'TypeScript', 'Vite'],
    specifications: [
      '支持 Vue 3 Composition API',
      '内置 TypeScript 支持',
      '快速热重载',
      '现代化构建工具'
    ]
  },
  {
    id: 2,
    name: 'React 工具包',
    description: '现代化的 React 开发工具集',
    detailDescription: '专为 React 开发者设计的工具包，包含了最新的 React 特性和开发工具，帮助您构建高性能的用户界面。',
    price: '¥399',
    icon: '⚛️',
    features: ['React 18', 'Next.js', 'Tailwind'],
    specifications: [
      '支持 React 18 新特性',
      '集成 Next.js 框架',
      'Tailwind CSS 样式系统',
      'ESLint 代码规范'
    ]
  },
  {
    id: 3,
    name: 'Node.js 后端框架',
    description: '高性能的 Node.js 后端解决方案',
    detailDescription: '基于 Node.js 的高性能后端框架，提供了完整的 API 开发解决方案，支持微服务架构和云原生部署。',
    price: '¥599',
    icon: '🟢',
    features: ['Express', 'MongoDB', 'Redis'],
    specifications: [
      'Express.js 框架',
      'MongoDB 数据库集成',
      'Redis 缓存支持',
      'JWT 身份验证',
      'API 文档自动生成'
    ]
  },
  {
    id: 4,
    name: '全栈开发包',
    description: '前后端一体化开发解决方案',
    detailDescription: '包含前端和后端的完整开发解决方案，提供了从用户界面到数据库的全栈开发体验，适合快速原型开发。',
    price: '¥999',
    icon: '🎯',
    features: ['Full Stack', 'Docker', 'CI/CD'],
    specifications: [
      '前后端分离架构',
      'Docker 容器化部署',
      'CI/CD 自动化流水线',
      '监控和日志系统',
      '自动化测试套件'
    ]
  }
])

const selectProduct = (product: Product) => {
  selectedProduct.value = selectedProduct.value?.id === product.id ? null : product
}
</script>

<style scoped>
.products {
  min-height: 100vh;
  padding: 2rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

h1 {
  text-align: center;
  font-size: 2.5rem;
  color: white;
  margin-bottom: 3rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.product-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-card.active {
  border: 2px solid #3498db;
  transform: translateY(-5px);
}

.product-image {
  font-size: 4rem;
  text-align: center;
  margin-bottom: 1rem;
}

.product-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.description {
  color: #7f8c8d;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.price {
  font-size: 1.5rem;
  font-weight: bold;
  color: #e74c3c;
  margin-bottom: 1rem;
}

.features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-tag {
  background: #ecf0f1;
  color: #2c3e50;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

.product-detail {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
}

.product-detail h2 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 2rem;
}

.detail-content {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 2rem;
  align-items: start;
}

.detail-image {
  font-size: 6rem;
  text-align: center;
}

.detail-description {
  color: #7f8c8d;
  line-height: 1.6;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.specifications h4 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.specifications ul {
  color: #7f8c8d;
  margin-bottom: 2rem;
}

.specifications li {
  margin-bottom: 0.5rem;
}

.actions {
  display: flex;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
  
  .actions {
    justify-content: center;
  }
}
</style>
