import { createApp, h } from 'vue';
import App from './App.vue';
import router from './router';
import './index.css';

// 创建一个包装组件来提供 Naive UI 的 providers
import { defineComponent } from 'vue';
import { NMessageProvider, NDialogProvider, NNotificationProvider } from 'naive-ui';

const AppWrapper = defineComponent({
  render() {
    return h(NMessageProvider, null, {
      default: () => h(NDialogProvider, null, {
        default: () => h(NNotificationProvider, null, {
          default: () => h(App)
        })
      })
    });
  }
});

const app = createApp(AppWrapper);

// 使用路由
app.use(router);

// 挂载应用
app.mount('#root');
