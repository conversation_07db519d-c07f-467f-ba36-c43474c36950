<template>
  <div class="about">
    <div class="container">
      <h1>关于我们</h1>
      <div class="content">
        <div class="intro">
          <h2>我们的故事</h2>
          <p>
            我们是一个专注于现代 Web 开发的团队，致力于使用最新的技术栈为客户提供高质量的解决方案。
            我们的技术栈包括 Vue 3、TypeScript、Rsbuild 等现代化工具。
          </p>
        </div>
        
        <div class="team">
          <h2>团队成员</h2>
          <div class="team-grid">
            <div class="member">
              <div class="avatar">👨‍💻</div>
              <h3>张三</h3>
              <p>前端开发工程师</p>
            </div>
            <div class="member">
              <div class="avatar">👩‍💻</div>
              <h3>李四</h3>
              <p>UI/UX 设计师</p>
            </div>
            <div class="member">
              <div class="avatar">👨‍💼</div>
              <h3>王五</h3>
              <p>项目经理</p>
            </div>
          </div>
        </div>

        <div class="values">
          <h2>我们的价值观</h2>
          <div class="values-list">
            <div class="value-item">
              <h3>🎯 专注</h3>
              <p>专注于提供最佳的用户体验</p>
            </div>
            <div class="value-item">
              <h3>🚀 创新</h3>
              <p>不断探索和应用新技术</p>
            </div>
            <div class="value-item">
              <h3>🤝 合作</h3>
              <p>团队协作，共同成长</p>
            </div>
            <div class="value-item">
              <h3>💡 学习</h3>
              <p>持续学习，保持技术领先</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const pageLoaded = ref(false)

onMounted(() => {
  pageLoaded.value = true
  console.log('About 页面已加载')
})
</script>

<style scoped>
.about {
  min-height: 100vh;
  padding: 2rem 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 2rem;
}

h1 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 3rem;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.intro, .team, .values {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.intro h2, .team h2, .values h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.intro p {
  color: #7f8c8d;
  line-height: 1.6;
  font-size: 1.1rem;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.member {
  text-align: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.avatar {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.member h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.member p {
  color: #7f8c8d;
}

.values-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;
}

.value-item {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.value-item h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.value-item p {
  color: #7f8c8d;
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .intro, .team, .values {
    padding: 1.5rem;
  }
}
</style>
