<template>
  <div class="contact">
    <div class="container">
      <h1>联系我们</h1>
      <div class="contact-content">
        <div class="contact-info">
          <h2>联系方式</h2>
          <div class="info-item">
            <div class="icon">📧</div>
            <div class="details">
              <h3>邮箱</h3>
              <p><EMAIL></p>
            </div>
          </div>
          <div class="info-item">
            <div class="icon">📱</div>
            <div class="details">
              <h3>电话</h3>
              <p>+86 138 0000 0000</p>
            </div>
          </div>
          <div class="info-item">
            <div class="icon">📍</div>
            <div class="details">
              <h3>地址</h3>
              <p>北京市朝阳区某某大厦 1001 室</p>
            </div>
          </div>
          <div class="info-item">
            <div class="icon">🕒</div>
            <div class="details">
              <h3>工作时间</h3>
              <p>周一至周五 9:00 - 18:00</p>
            </div>
          </div>
        </div>

        <div class="contact-form">
          <h2>发送消息</h2>
          <form @submit.prevent="submitForm">
            <div class="form-group">
              <label for="name">姓名</label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                placeholder="请输入您的姓名"
              />
            </div>
            <div class="form-group">
              <label for="email">邮箱</label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                required
                placeholder="请输入您的邮箱"
              />
            </div>
            <div class="form-group">
              <label for="subject">主题</label>
              <input
                id="subject"
                v-model="form.subject"
                type="text"
                required
                placeholder="请输入消息主题"
              />
            </div>
            <div class="form-group">
              <label for="message">消息内容</label>
              <textarea
                id="message"
                v-model="form.message"
                required
                rows="5"
                placeholder="请输入您的消息内容"
              ></textarea>
            </div>
            <button type="submit" class="submit-btn" :disabled="isSubmitting">
              {{ isSubmitting ? '发送中...' : '发送消息' }}
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

const form = reactive({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const isSubmitting = ref(false)

const submitForm = async () => {
  isSubmitting.value = true
  
  // 模拟表单提交
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    alert('消息发送成功！我们会尽快回复您。')
    
    // 重置表单
    Object.assign(form, {
      name: '',
      email: '',
      subject: '',
      message: ''
    })
  } catch (error) {
    alert('发送失败，请稍后重试。')
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style scoped>
.contact {
  min-height: 100vh;
  padding: 2rem 0;
  background: #f8f9fa;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

h1 {
  text-align: center;
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 3rem;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.contact-info, .contact-form {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.contact-info h2, .contact-form h2 {
  color: #2c3e50;
  margin-bottom: 2rem;
  font-size: 1.8rem;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.icon {
  font-size: 2rem;
  margin-right: 1rem;
  width: 50px;
  text-align: center;
}

.details h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.details p {
  color: #7f8c8d;
  margin: 0;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #2c3e50;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
}

.submit-btn {
  width: 100%;
  padding: 1rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.submit-btn:hover:not(:disabled) {
  background: #2980b9;
}

.submit-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .contact-info, .contact-form {
    padding: 1.5rem;
  }
}
</style>
