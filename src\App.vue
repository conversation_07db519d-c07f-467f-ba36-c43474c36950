<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  NLayout,
  NLayoutHeader,
  NLayoutContent,
  NLayoutSider,
  NMenu,
  NSpace,
  NText,
  NButton,
  useMessage
} from 'naive-ui'

const router = useRouter()
const route = useRoute()
const message = useMessage()

const collapsed = ref(false)

// 菜单选项
const menuOptions = [
  {
    label: '🏠 首页',
    key: '/'
  },
  {
    label: '🎮 游戏库',
    key: '/games'
  },
  {
    label: 'ℹ️ 关于我们',
    key: '/about'
  },
  {
    label: '📦 产品展示',
    key: '/products'
  },
  {
    label: '📞 联系我们',
    key: '/contact'
  }
]

// 处理菜单点击
const handleMenuSelect = (key: string) => {
  router.push(key)
}

// 获取当前选中的菜单项
const selectedKey = computed(() => route.path)
</script>

<template>
  <n-layout has-sider style="height: 100vh">
    <!-- 侧边栏 -->
    <n-layout-sider
      bordered
      collapse-mode="width"
      :collapsed-width="64"
      :width="240"
      :collapsed="collapsed"
      show-trigger
      @collapse="collapsed = true"
      @expand="collapsed = false"
    >
      <div class="logo">
        <n-space align="center" justify="center" style="padding: 20px">
          <n-text style="font-size: 24px">🎮</n-text>
          <n-text v-if="!collapsed" strong style="font-size: 18px">
            游戏助手
          </n-text>
        </n-space>
      </div>

      <n-menu
        :collapsed="collapsed"
        :collapsed-width="64"
        :collapsed-icon-size="22"
        :options="menuOptions"
        :value="selectedKey"
        @update:value="handleMenuSelect"
      />
    </n-layout-sider>

    <!-- 主内容区域 -->
    <n-layout>
      <!-- 顶部导航栏 -->
      <n-layout-header bordered style="height: 64px; padding: 0 24px">
        <div class="header-content">
          <n-space align="center" justify="space-between" style="height: 100%">
            <n-text strong style="font-size: 16px">
              {{ route.meta?.title || '游戏助手' }}
            </n-text>
            <n-space>
              <n-button text @click="message.info('设置功能开发中')">
                设置
              </n-button>
              <n-button text @click="message.info('帮助功能开发中')">
                帮助
              </n-button>
            </n-space>
          </n-space>
        </div>
      </n-layout-header>

      <!-- 页面内容 -->
      <n-layout-content style="padding: 0; background: #f5f5f5">
        <router-view />
      </n-layout-content>
    </n-layout>
  </n-layout>
</template>

<style scoped>
.logo {
  border-bottom: 1px solid #e0e0e6;
}

.header-content {
  height: 100%;
}
</style>
