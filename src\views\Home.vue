<template>
  <div class="home">
    <div class="hero">
      <h1>欢迎来到首页</h1>
      <p>这是一个使用 Vue 3 + Vue Router + Rsbuild 构建的示例应用</p>
      <div class="features">
        <div class="feature-card">
          <h3>🚀 快速开发</h3>
          <p>基于 Rsbuild 的快速构建工具</p>
        </div>
        <div class="feature-card">
          <h3>⚡ Vue 3</h3>
          <p>使用最新的 Vue 3 Composition API</p>
        </div>
        <div class="feature-card">
          <h3>🛣️ Vue Router</h3>
          <p>强大的客户端路由解决方案</p>
        </div>
      </div>
      <div class="actions">
        <router-link to="/about" class="btn btn-primary">了解更多</router-link>
        <router-link to="/products" class="btn btn-secondary">查看产品</router-link>
        <button @click="refreshPage" class="btn btn-refresh">🔄 刷新页面</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('Home 页面已加载')
})

// 刷新页面方法
const refreshPage = () => {
  window.location.reload()
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.hero {
  text-align: center;
  max-width: 800px;
}

.hero h1 {
  font-size: 3rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.hero > p {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin-bottom: 3rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.feature-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.feature-card p {
  color: #7f8c8d;
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn-refresh {
  background: #e67e22;
  color: white;
}

.btn-refresh:hover {
  background: #d35400;
}
</style>
